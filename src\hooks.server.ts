import type { Handle } from '@sveltejs/kit';
import * as auth from '$lib/server/auth';

const handleAuth: Handle = async ({ event, resolve }) => {
	// BYPASS AUTHENTICATION - Create a mock user for development
	event.locals.user = {
		id: 'dev-user-123',
		email: '<EMAIL>',
		name: 'Development User',
		username: 'devuser'
	};
	event.locals.session = {
		id: 'dev-session-123',
		userId: 'dev-user-123',
		expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
	};

	return resolve(event);
};

export const handle: Handle = handleAuth;
