{"name": "personal-finance", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/adapter-node": "^5.2.10", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "bits-ui": "^2.8.10", "clsx": "^2.1.1", "drizzle-kit": "^0.30.2", "mdsvex": "^0.12.3", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.4", "typescript": "^5.0.0", "vite": "^6.2.6", "vite-plugin-devtools-json": "^0.2.0"}, "dependencies": {"@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "date-fns": "^4.1.0", "drizzle-orm": "^0.40.0", "postgres": "^3.4.5", "resend": "^4.6.0", "zod": "^3.25.74"}}