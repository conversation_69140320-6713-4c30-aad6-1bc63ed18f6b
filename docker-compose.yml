services:
  postgres:
    image: postgres:16-alpine
    container_name: saldoify-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: saldoify
      POSTGRES_PASSWORD: saldoify_password
      POSTGRES_DB: saldoify
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5436:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U saldoify"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        DATABASE_URL: *****************************************************/saldoify
    container_name: saldoify-app
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      DATABASE_URL: *****************************************************/saldoify
      NODE_ENV: production
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data: