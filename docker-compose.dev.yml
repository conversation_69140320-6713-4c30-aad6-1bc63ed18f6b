services:
  postgres:
    image: postgres:16-alpine
    container_name: saldoify-db-dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: saldoify
      POSTGRES_PASSWORD: saldoify_password
      POSTGRES_DB: saldoify
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    ports:
      - "5436:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U saldoify"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data_dev: